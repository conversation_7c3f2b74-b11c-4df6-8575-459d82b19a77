import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';

class CompleteYourProfileScreen extends StatefulWidget {
  const CompleteYourProfileScreen({super.key});

  @override
  State<CompleteYourProfileScreen> createState() =>
      _CompleteYourProfileScreenState();
}

class _CompleteYourProfileScreenState extends State<CompleteYourProfileScreen> {
  final _formKey = GlobalKey<FormState>(); // Keep for potential future use
  final countryPicker = const FlCountryCodePicker(
    title: SizedBox.shrink(),
  );
  TextEditingController genderC = TextEditingController();
  TextEditingController occupationC = TextEditingController();
  TextEditingController addressC = TextEditingController();
  TextEditingController dobC = TextEditingController();

  FocusNode genderF = FocusNode();
  FocusNode occupationF = FocusNode();
  FocusNode addressF = FocusNode();
  FocusNode dobF = FocusNode();

  DateTime? selectedDate;

  // Form validation state tracking
  bool _isFormValid = false;
  final Map<String, bool> _fieldValidationStates = {
    'gender': false,
    'occupation': false,
    'address': false,
    'dob': false,
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    genderC.dispose();
    occupationC.dispose();
    addressC.dispose();
    dobC.dispose();

    genderF.dispose();
    occupationF.dispose();
    addressF.dispose();
    dobF.dispose();

    super.dispose();
  }

  /// Update form validation state when individual field validation changes
  void _updateFormValidation(String fieldName, bool isValid) {
    setState(() {
      _fieldValidationStates[fieldName] = isValid;
      _isFormValid = _fieldValidationStates.values.every((valid) => valid) &&
          _fieldValidationStates.length == 4; // All 4 fields must be valid
    });
  }

  /// Validate all fields programmatically (for form submission)
  Future<bool> _validateAllFields() async {
    // For fields with custom validation rules, we rely on the real-time validation
    // For read-only fields (gender, dob), we check if they have values
    bool genderValid = genderC.text.trim().isNotEmpty;
    bool dobValid = dobC.text.trim().isNotEmpty;

    _updateFormValidation('gender', genderValid);
    _updateFormValidation('dob', dobValid);

    return _isFormValid;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Complete Your Profile',
            subtitle: "Please enter the information below",
          ),
          const YBox(40),
          CustomTextField(
            controller: genderC,
            focusNode: genderF,
            labelText: 'Gender',
            showLabelHeader: true,
            isReadOnly: true,
            validationRules: const [
              RequiredRule(fieldName: 'gender'),
            ],
            enableRealTimeValidation: true,
            suffixIcon: const Icon(
              Iconsax.arrow_down_1,
            ),
            onChanged: (v) {
              _updateFormValidation('gender', v.trim().isNotEmpty);
              setState(() {});
            },
            onValidationChanged: (result) {
              _updateFormValidation('gender', result.isValid);
            },
            onTap: () async {
              final r = await ModalWrapper.bottomSheet(
                context: context,
                widget: const SelectionModal(
                    selections: ['Male', 'Female', 'Other']),
              );

              if (r is String && context.mounted) {
                genderC.text = r;
                _updateFormValidation('gender', r.trim().isNotEmpty);
                setState(() {});
              }
            },
          ),
          const YBox(20),
          CustomTextField(
            controller: occupationC,
            focusNode: occupationF,
            labelText: 'Occupation',
            showLabelHeader: true,
            validationRules: ValidationRules.requiredWithLength(
              fieldName: 'occupation',
              minLength: 2,
              maxLength: 50,
              requiredMessage: 'Please enter your occupation',
              minLengthMessage: 'Occupation must be at least 2 characters',
              maxLengthMessage: 'Occupation must not exceed 50 characters',
            ),
            enableRealTimeValidation: true,
            onChanged: (v) => setState(() {}),
            onValidationChanged: (result) {
              _updateFormValidation('occupation', result.isValid);
            },
          ),
          const YBox(20),
          CustomTextField(
            controller: addressC,
            focusNode: addressF,
            labelText: 'Address',
            showLabelHeader: true,
            validationRules: ValidationRules.requiredWithLength(
              fieldName: 'address',
              minLength: 5,
              maxLength: 200,
              requiredMessage: 'Please enter your address',
              minLengthMessage: 'Address must be at least 5 characters',
              maxLengthMessage: 'Address must not exceed 200 characters',
            ),
            enableRealTimeValidation: true,
            onChanged: (v) => setState(() {}),
            onValidationChanged: (result) {
              _updateFormValidation('address', result.isValid);
            },
          ),
          const YBox(20),
          CustomTextField(
            controller: dobC,
            focusNode: dobF,
            labelText: 'Date of birth',
            showLabelHeader: true,
            isReadOnly: true,
            validationRules: const [
              RequiredRule(
                fieldName: 'date of birth',
                customErrorMessage: 'Please select your date of birth',
              ),
            ],
            enableRealTimeValidation: true,
            suffixIcon: const Icon(
              Iconsax.calendar_1,
            ),
            onChanged: (v) {
              _updateFormValidation('dob', v.trim().isNotEmpty);
              setState(() {});
            },
            onValidationChanged: (result) {
              _updateFormValidation('dob', result.isValid);
            },
            onTap: () {
              CustomCupertinoDatePicker(
                context: context,
                // Set initial date to 25 years ago (reasonable default for adults)
                initialDateTime:
                    DateTime.now().subtract(const Duration(days: 365 * 25)),
                // Allow dates from 100 years ago to 18 years ago (minimum age requirement)
                minimumDate:
                    DateTime.now().subtract(const Duration(days: 365 * 100)),
                maximumDate:
                    DateTime.now().subtract(const Duration(days: 365 * 18)),
                onDateTimeChanged: (dateTime) {
                  dobC.text = AppUtils.dayWithSuffixMonthAndYear(dateTime);
                  selectedDate = dateTime;
                  _updateFormValidation('dob', true);
                  setState(() {});
                },
              ).show();
            },
          ),
          const YBox(60),
          CustomBtn.solid(
            text: 'Continue',
            online: _isFormValid,
            onTap: _isFormValid
                ? () async {
                    // Perform final validation check
                    bool isValid = await _validateAllFields();

                    if (!isValid) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please complete all required fields'),
                          backgroundColor: AppColors.red,
                        ),
                      );
                      return;
                    }

                    // Show success message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Profile completed successfully!'),
                        backgroundColor: AppColors.green00,
                      ),
                    );

                    // Navigate to next screen
                    // Navigator.pushNamed(context, RoutePath.verifyYourIdentityScreen);
                  }
                : null,
          ),
        ],
      ),
    );
  }
}
